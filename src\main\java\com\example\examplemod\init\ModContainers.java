package com.example.examplemod.init;

import com.example.examplemod.container.SuperDispenserContainer;
import net.minecraft.inventory.container.ContainerType;
import net.minecraftforge.common.extensions.IForgeContainerType;
import net.minecraftforge.registries.IForgeRegistry;

public class ModContainers {

    public static final ContainerType<SuperDispenserContainer> SUPER_DISPENSER = IForgeContainerType.create(SuperDispenserContainer::new);

    public static void register(IForgeRegistry<ContainerType<?>> registry) {
        registry.register(SUPER_DISPENSER.setRegistryName("examplemod", "super_dispenser"));
    }
}
