package com.example.examplemod.tileentity;

import com.example.examplemod.init.ModTileEntities;
import net.minecraft.block.BlockState;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.player.PlayerInventory;
import net.minecraft.inventory.ItemStackHelper;
import net.minecraft.inventory.container.Container;
import net.minecraft.item.ItemStack;
import net.minecraft.nbt.CompoundNBT;
import net.minecraft.tileentity.DispenserTileEntity;
import net.minecraft.util.Direction;
import net.minecraft.util.NonNullList;
import net.minecraft.util.text.ITextComponent;
import net.minecraft.util.text.TranslationTextComponent;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.util.LazyOptional;
import net.minecraftforge.items.CapabilityItemHandler;
import net.minecraftforge.items.IItemHandler;
import net.minecraftforge.items.wrapper.InvWrapper;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

public class SuperDispenserTileEntity extends DispenserTileEntity {

    private NonNullList<ItemStack> items = NonNullList.withSize(54, ItemStack.EMPTY); // 54 slots like 2 chests
    private final LazyOptional<IItemHandler> handler = LazyOptional.of(() -> new InvWrapper(this));

    public SuperDispenserTileEntity() {
        super(ModTileEntities.SUPER_DISPENSER);
    }

    @Override
    protected NonNullList<ItemStack> getItems() {
        return this.items;
    }

    @Override
    protected void setItems(NonNullList<ItemStack> items) {
        this.items = items;
    }

    @Override
    public int getContainerSize() {
        return 54;
    }

    @Override
    public void load(BlockState state, CompoundNBT nbt) {
        super.load(state, nbt);
        this.items = NonNullList.withSize(this.getContainerSize(), ItemStack.EMPTY);
        ItemStackHelper.loadAllItems(nbt, this.items);
    }

    @Override
    public CompoundNBT save(CompoundNBT compound) {
        super.save(compound);
        ItemStackHelper.saveAllItems(compound, this.items);
        return compound;
    }

    @Override
    public void setItem(int index, ItemStack stack) {
        super.setItem(index, stack);
        this.setChanged(); // Ensure the tile entity is marked as dirty for saving
    }

    @Override
    public ItemStack removeItem(int index, int count) {
        ItemStack result = super.removeItem(index, count);
        this.setChanged(); // Ensure the tile entity is marked as dirty for saving
        return result;
    }

    @Override
    public ItemStack removeItemNoUpdate(int index) {
        ItemStack result = super.removeItemNoUpdate(index);
        this.setChanged(); // Ensure the tile entity is marked as dirty for saving
        return result;
    }

    @Nonnull
    @Override
    public <T> LazyOptional<T> getCapability(@Nonnull Capability<T> cap, @Nullable Direction side) {
        if (cap == CapabilityItemHandler.ITEM_HANDLER_CAPABILITY) {
            return handler.cast();
        }
        return super.getCapability(cap, side);
    }

    public boolean canPlayerUse(PlayerEntity player) {
        return this.level.getBlockEntity(this.worldPosition) == this &&
               player.distanceToSqr(this.worldPosition.getX() + 0.5, this.worldPosition.getY() + 0.5, this.worldPosition.getZ() + 0.5) <= 64;
    }

    @Override
    public int getRandomSlot() {
        // Override to select from all 54 slots instead of just 9
        // First, find all non-empty slots
        java.util.List<Integer> nonEmptySlots = new java.util.ArrayList<>();
        for (int i = 0; i < this.getContainerSize(); i++) {
            if (!this.getItem(i).isEmpty()) {
                nonEmptySlots.add(i);
            }
        }

        // If no items, return -1 (vanilla behavior)
        if (nonEmptySlots.isEmpty()) {
            return -1;
        }

        // Return a random non-empty slot
        return nonEmptySlots.get(this.level.random.nextInt(nonEmptySlots.size()));
    }

    @Override
    protected ITextComponent getDefaultName() {
        return new TranslationTextComponent("container.examplemod.super_dispenser");
    }

    @Override
    protected Container createMenu(int windowId, PlayerInventory playerInventory) {
        return net.minecraft.inventory.container.ChestContainer.sixRows(windowId, playerInventory, this);
    }
}
