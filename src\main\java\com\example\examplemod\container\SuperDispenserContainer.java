package com.example.examplemod.container;

import com.example.examplemod.init.ModContainers;
import com.example.examplemod.tileentity.SuperDispenserTileEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.player.PlayerInventory;
import net.minecraft.inventory.container.Container;
import net.minecraft.inventory.container.Slot;
import net.minecraft.item.ItemStack;
import net.minecraft.network.PacketBuffer;
import net.minecraft.tileentity.TileEntity;
import net.minecraft.util.IWorldPosCallable;
import net.minecraft.util.math.BlockPos;

public class SuperDispenserContainer extends Container {
    
    private final SuperDispenserTileEntity tileEntity;
    private final IWorldPosCallable canInteractWithCallable;
    
    public SuperDispenserContainer(int windowId, PlayerInventory playerInventory, SuperDispenserTileEntity tileEntity) {
        super(ModContainers.SUPER_DISPENSER, windowId);
        this.tileEntity = tileEntity;
        this.canInteractWithCallable = tileEntity != null && tileEntity.getLevel() != null ?
            IWorldPosCallable.create(tileEntity.getLevel(), tileEntity.getBlockPos()) :
            IWorldPosCallable.NULL;

        // Add super dispenser slots (9x6 grid like 2 chests)
        for (int row = 0; row < 6; row++) {
            for (int col = 0; col < 9; col++) {
                this.addSlot(new Slot(tileEntity, col + row * 9, 8 + col * 18, 18 + row * 18));
            }
        }
        
        // Add player inventory slots (moved down to accommodate larger dispenser)
        for (int row = 0; row < 3; row++) {
            for (int col = 0; col < 9; col++) {
                this.addSlot(new Slot(playerInventory, col + row * 9 + 9, 8 + col * 18, 140 + row * 18));
            }
        }
        
        // Add player hotbar slots
        for (int col = 0; col < 9; col++) {
            this.addSlot(new Slot(playerInventory, col, 8 + col * 18, 198));
        }
    }
    
    public SuperDispenserContainer(int windowId, PlayerInventory playerInventory, PacketBuffer data) {
        this(windowId, playerInventory, getTileEntity(playerInventory, data));
    }
    
    private static SuperDispenserTileEntity getTileEntity(PlayerInventory playerInventory, PacketBuffer data) {
        try {
            BlockPos pos = data.readBlockPos();
            if (playerInventory != null && playerInventory.player != null && playerInventory.player.level != null) {
                TileEntity tileEntity = playerInventory.player.level.getBlockEntity(pos);
                if (tileEntity instanceof SuperDispenserTileEntity) {
                    return (SuperDispenserTileEntity) tileEntity;
                }
            }
            // Create a dummy tile entity if we can't find the real one (for client-side)
            SuperDispenserTileEntity dummy = new SuperDispenserTileEntity();
            if (playerInventory != null && playerInventory.player != null && playerInventory.player.level != null) {
                dummy.setLevelAndPosition(playerInventory.player.level, pos);
            }
            return dummy;
        } catch (Exception e) {
            // Fallback - create a basic dummy tile entity
            return new SuperDispenserTileEntity();
        }
    }
    
    @Override
    public boolean stillValid(PlayerEntity player) {
        if (tileEntity == null) return false;
        return stillValid(canInteractWithCallable, player, tileEntity.getBlockState().getBlock());
    }
    
    @Override
    public ItemStack quickMoveStack(PlayerEntity player, int index) {
        ItemStack itemstack = ItemStack.EMPTY;
        Slot slot = this.slots.get(index);
        
        if (slot != null && slot.hasItem()) {
            ItemStack itemstack1 = slot.getItem();
            itemstack = itemstack1.copy();
            
            if (index < 54) {
                // Moving from super dispenser to player inventory
                if (!this.moveItemStackTo(itemstack1, 54, this.slots.size(), true)) {
                    return ItemStack.EMPTY;
                }
            } else {
                // Moving from player inventory to super dispenser
                if (!this.moveItemStackTo(itemstack1, 0, 54, false)) {
                    return ItemStack.EMPTY;
                }
            }
            
            if (itemstack1.isEmpty()) {
                slot.set(ItemStack.EMPTY);
            } else {
                slot.setChanged();
            }
        }
        
        return itemstack;
    }
}
