package com.example.examplemod.init;

import com.example.examplemod.items.SuperDispenserItem;
import net.minecraft.item.Item;
import net.minecraft.item.ItemGroup;
import net.minecraftforge.registries.IForgeRegistry;

public class ModItems {

    public static final SuperDispenserItem SUPER_DISPENSER = new SuperDispenserItem(ModBlocks.SUPER_DISPENSER, new Item.Properties().tab(ItemGroup.TAB_REDSTONE));

    public static void register(IForgeRegistry<Item> registry) {
        registry.register(SUPER_DISPENSER.setRegistryName("examplemod", "super_dispenser"));
    }
}
