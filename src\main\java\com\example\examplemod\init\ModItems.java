package com.example.examplemod.init;

import net.minecraft.item.BlockItem;
import net.minecraft.item.Item;
import net.minecraft.item.ItemGroup;
import net.minecraftforge.registries.IForgeRegistry;

public class ModItems {

    public static final BlockItem SUPER_DISPENSER = new BlockItem(ModBlocks.SUPER_DISPENSER, new Item.Properties().tab(ItemGroup.TAB_REDSTONE));

    public static void register(IForgeRegistry<Item> registry) {
        registry.register(SUPER_DISPENSER.setRegistryName("examplemod", "super_dispenser"));
    }
}
