package com.example.examplemod.blocks;

import com.example.examplemod.init.ModTileEntities;
import com.example.examplemod.tileentity.SuperDispenserTileEntity;
import net.minecraft.block.BlockState;
import net.minecraft.block.DispenserBlock;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.inventory.container.INamedContainerProvider;
import net.minecraft.particles.ParticleTypes;
import net.minecraft.tileentity.TileEntity;
import net.minecraft.util.ActionResultType;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.BlockRayTraceResult;
import net.minecraft.world.IBlockReader;
import net.minecraft.world.World;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

import javax.annotation.Nullable;
import java.util.Random;

public class SuperDispenserBlock extends DispenserBlock {

    public SuperDispenserBlock(Properties properties) {
        super(properties.lightLevel((state) -> 12)); // Make it glow brightly with light level 12
    }

    @Override
    public boolean hasTileEntity(BlockState state) {
        return true;
    }

    @Nullable
    @Override
    public TileEntity createTileEntity(BlockState state, IBlockReader world) {
        return ModTileEntities.SUPER_DISPENSER.create();
    }

    @Override
    public ActionResultType use(BlockState state, World world, BlockPos pos, PlayerEntity player, Hand hand, BlockRayTraceResult hit) {
        if (world.isClientSide) {
            return ActionResultType.SUCCESS;
        } else if (!state.getValue(TRIGGERED)) {
            // Only open GUI if not triggered (same as vanilla dispenser behavior)
            TileEntity tileEntity = world.getBlockEntity(pos);
            if (tileEntity instanceof SuperDispenserTileEntity) {
                player.openMenu((INamedContainerProvider)tileEntity);
            }
            return ActionResultType.CONSUME;
        }
        return ActionResultType.PASS;
    }

    @Override
    @OnlyIn(Dist.CLIENT)
    public void animateTick(BlockState state, World world, BlockPos pos, Random random) {
        // Add enchantment particles around the super dispenser
        if (random.nextInt(8) == 0) {
            double x = pos.getX() + 0.5 + (random.nextDouble() - 0.5) * 1.2;
            double y = pos.getY() + 0.5 + (random.nextDouble() - 0.5) * 1.2;
            double z = pos.getZ() + 0.5 + (random.nextDouble() - 0.5) * 1.2;

            // Add enchantment table particles for magical effect
            world.addParticle(ParticleTypes.ENCHANT, x, y, z,
                (random.nextDouble() - 0.5) * 0.1,
                (random.nextDouble() - 0.5) * 0.1,
                (random.nextDouble() - 0.5) * 0.1);
        }

        // Add frequent golden sparkles
        if (random.nextInt(12) == 0) {
            double x = pos.getX() + 0.5 + (random.nextDouble() - 0.5) * 0.8;
            double y = pos.getY() + 0.5 + (random.nextDouble() - 0.5) * 0.8;
            double z = pos.getZ() + 0.5 + (random.nextDouble() - 0.5) * 0.8;

            // Add end rod particles for golden sparkle effect
            world.addParticle(ParticleTypes.END_ROD, x, y, z, 0, 0.02, 0);
        }

        // Add golden dust particles
        if (random.nextInt(10) == 0) {
            double x = pos.getX() + 0.5 + (random.nextDouble() - 0.5) * 1.0;
            double y = pos.getY() + 0.5 + (random.nextDouble() - 0.5) * 1.0;
            double z = pos.getZ() + 0.5 + (random.nextDouble() - 0.5) * 1.0;

            // Add happy villager particles for golden dust effect
            world.addParticle(ParticleTypes.HAPPY_VILLAGER, x, y, z, 0, 0.05, 0);
        }

        // Add extra bright golden sparkles
        if (random.nextInt(6) == 0) {
            double x = pos.getX() + 0.5 + (random.nextDouble() - 0.5) * 0.6;
            double y = pos.getY() + 0.5 + (random.nextDouble() - 0.5) * 0.6;
            double z = pos.getZ() + 0.5 + (random.nextDouble() - 0.5) * 0.6;

            // Add crit particles for bright golden sparkles
            world.addParticle(ParticleTypes.CRIT, x, y, z,
                (random.nextDouble() - 0.5) * 0.05,
                0.1,
                (random.nextDouble() - 0.5) * 0.05);
        }
    }

    @Override
    public void neighborChanged(BlockState state, World world, BlockPos pos, net.minecraft.block.Block block, BlockPos fromPos, boolean isMoving) {
        // Call the parent method first to handle normal dispenser behavior
        super.neighborChanged(state, world, pos, block, fromPos, isMoving);

        // Add extra visual effects when the dispenser is triggered
        if (!world.isClientSide && state.getValue(TRIGGERED)) {
            // Add burst of particles when dispensing
            Random random = world.random;
            for (int i = 0; i < 10; i++) {
                double x = pos.getX() + 0.5 + (random.nextDouble() - 0.5) * 0.8;
                double y = pos.getY() + 0.5 + (random.nextDouble() - 0.5) * 0.8;
                double z = pos.getZ() + 0.5 + (random.nextDouble() - 0.5) * 0.8;

                // Send particles to all nearby players
                world.addParticle(ParticleTypes.FIREWORK, x, y, z,
                    (random.nextDouble() - 0.5) * 0.2,
                    (random.nextDouble() - 0.5) * 0.2,
                    (random.nextDouble() - 0.5) * 0.2);
            }
        }
    }
}
