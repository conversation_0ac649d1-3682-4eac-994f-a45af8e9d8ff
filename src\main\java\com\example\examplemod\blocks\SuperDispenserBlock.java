package com.example.examplemod.blocks;

import com.example.examplemod.init.ModTileEntities;
import com.example.examplemod.tileentity.SuperDispenserTileEntity;
import net.minecraft.block.BlockState;
import net.minecraft.block.DispenserBlock;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.inventory.container.INamedContainerProvider;
import net.minecraft.tileentity.TileEntity;
import net.minecraft.util.ActionResultType;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.BlockRayTraceResult;
import net.minecraft.world.IBlockReader;
import net.minecraft.world.World;

import javax.annotation.Nullable;

public class SuperDispenserBlock extends DispenserBlock {

    public SuperDispenserBlock(Properties properties) {
        super(properties);
    }

    @Override
    public boolean hasTileEntity(BlockState state) {
        return true;
    }

    @Nullable
    @Override
    public TileEntity createTileEntity(BlockState state, IBlockReader world) {
        return ModTileEntities.SUPER_DISPENSER.create();
    }

    @Override
    public ActionResultType use(BlockState state, World world, BlockPos pos, PlayerEntity player, Hand hand, BlockRayTraceResult hit) {
        if (world.isClientSide) {
            return ActionResultType.SUCCESS;
        } else if (!state.getValue(TRIGGERED)) {
            // Only open GUI if not triggered (same as vanilla dispenser behavior)
            TileEntity tileEntity = world.getBlockEntity(pos);
            if (tileEntity instanceof SuperDispenserTileEntity) {
                player.openMenu((INamedContainerProvider)tileEntity);
            }
            return ActionResultType.CONSUME;
        }
        return ActionResultType.PASS;
    }
}
