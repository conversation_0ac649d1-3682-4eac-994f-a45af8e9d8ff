package com.example.examplemod.items;

import net.minecraft.block.Block;
import net.minecraft.item.BlockItem;
import net.minecraft.item.ItemStack;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

public class SuperDispenserItem extends BlockItem {
    
    public SuperDispenserItem(Block block, Properties properties) {
        super(block, properties);
    }
    
    @Override
    @OnlyIn(Dist.CLIENT)
    public boolean isFoil(ItemStack stack) {
        // Always show enchantment glint
        return true;
    }
}
