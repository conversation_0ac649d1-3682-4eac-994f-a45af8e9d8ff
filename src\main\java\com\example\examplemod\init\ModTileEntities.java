package com.example.examplemod.init;

import com.example.examplemod.tileentity.SuperDispenserTileEntity;
import net.minecraft.tileentity.TileEntityType;
import net.minecraftforge.registries.IForgeRegistry;

public class ModTileEntities {

    public static final TileEntityType<SuperDispenserTileEntity> SUPER_DISPENSER = TileEntityType.Builder.of(
            SuperDispenserTileEntity::new, ModBlocks.SUPER_DISPENSER).build(null);

    public static void register(IForgeRegistry<TileEntityType<?>> registry) {
        registry.register(SUPER_DISPENSER.setRegistryName("examplemod", "super_dispenser"));
    }
}
