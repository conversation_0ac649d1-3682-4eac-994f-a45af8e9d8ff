package com.example.examplemod.init;

import com.example.examplemod.blocks.DispenserBlock;
import com.example.examplemod.blocks.SuperDispenserBlock;
import net.minecraft.block.Block;
import net.minecraft.block.material.Material;
import net.minecraft.block.material.MaterialColor;
import net.minecraftforge.registries.IForgeRegistry;

public class ModBlocks {
    
    public static final DispenserBlock DISPENSER = new DispenserBlock(Block.Properties.of(Material.STONE, MaterialColor.STONE)
            .strength(3.5F)
            .requiresCorrectToolForDrops());
    
    public static final SuperDispenserBlock SUPER_DISPENSER = new SuperDispenserBlock(Block.Properties.of(Material.STONE, MaterialColor.STONE)
            .strength(3.5F)
            .requiresCorrectToolForDrops());
    
    public static void register(IForgeRegistry<Block> registry) {
        registry.register(DISPENSER.setRegistryName("examplemod", "dispenser"));
        registry.register(SUPER_DISPENSER.setRegistryName("examplemod", "super_dispenser"));
    }
}
