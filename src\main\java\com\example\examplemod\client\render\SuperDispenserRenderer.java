package com.example.examplemod.client.render;

import com.example.examplemod.tileentity.SuperDispenserTileEntity;
import com.mojang.blaze3d.matrix.MatrixStack;
import com.mojang.blaze3d.vertex.IVertexBuilder;
import net.minecraft.client.renderer.IRenderTypeBuffer;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.tileentity.TileEntityRenderer;
import net.minecraft.client.renderer.tileentity.TileEntityRendererDispatcher;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.math.vector.Matrix4f;
import net.minecraft.util.math.vector.Vector3f;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public class SuperDispenserRenderer extends TileEntityRenderer<SuperDispenserTileEntity> {
    
    private static final ResourceLocation ENCHANTMENT_GLINT_TEXTURE = new ResourceLocation("textures/misc/enchanted_item_glint.png");
    
    public SuperDispenserRenderer(TileEntityRendererDispatcher rendererDispatcher) {
        super(rendererDispatcher);
    }
    
    @Override
    public void render(SuperDispenserTileEntity tileEntity, float partialTicks, MatrixStack matrixStack, IRenderTypeBuffer buffer, int combinedLight, int combinedOverlay) {
        // Add enchantment glint effect
        renderEnchantmentGlint(matrixStack, buffer, combinedLight, combinedOverlay);
        
        // Add glowing outline effect
        renderGlowingOutline(matrixStack, buffer, partialTicks);
    }
    
    private void renderEnchantmentGlint(MatrixStack matrixStack, IRenderTypeBuffer buffer, int combinedLight, int combinedOverlay) {
        matrixStack.pushPose();
        
        // Scale slightly larger than the block for the glint effect
        matrixStack.scale(1.02f, 1.02f, 1.02f);
        matrixStack.translate(-0.01f, -0.01f, -0.01f);
        
        // Get the enchantment glint render type
        IVertexBuilder vertexBuilder = buffer.getBuffer(RenderType.glint());
        Matrix4f matrix = matrixStack.last().pose();
        
        // Render the glint on all faces of the block
        // Top face
        vertexBuilder.vertex(matrix, 0, 1, 0).color(255, 215, 0, 128).uv(0, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 1, 0).endVertex();
        vertexBuilder.vertex(matrix, 1, 1, 0).color(255, 215, 0, 128).uv(1, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 1, 0).endVertex();
        vertexBuilder.vertex(matrix, 1, 1, 1).color(255, 215, 0, 128).uv(1, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 1, 0).endVertex();
        vertexBuilder.vertex(matrix, 0, 1, 1).color(255, 215, 0, 128).uv(0, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 1, 0).endVertex();
        
        // Bottom face
        vertexBuilder.vertex(matrix, 0, 0, 1).color(255, 215, 0, 128).uv(0, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, -1, 0).endVertex();
        vertexBuilder.vertex(matrix, 1, 0, 1).color(255, 215, 0, 128).uv(1, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, -1, 0).endVertex();
        vertexBuilder.vertex(matrix, 1, 0, 0).color(255, 215, 0, 128).uv(1, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, -1, 0).endVertex();
        vertexBuilder.vertex(matrix, 0, 0, 0).color(255, 215, 0, 128).uv(0, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, -1, 0).endVertex();
        
        // North face
        vertexBuilder.vertex(matrix, 0, 0, 0).color(255, 215, 0, 128).uv(0, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 0, -1).endVertex();
        vertexBuilder.vertex(matrix, 1, 0, 0).color(255, 215, 0, 128).uv(1, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 0, -1).endVertex();
        vertexBuilder.vertex(matrix, 1, 1, 0).color(255, 215, 0, 128).uv(1, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 0, -1).endVertex();
        vertexBuilder.vertex(matrix, 0, 1, 0).color(255, 215, 0, 128).uv(0, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 0, -1).endVertex();
        
        // South face
        vertexBuilder.vertex(matrix, 1, 0, 1).color(255, 215, 0, 128).uv(0, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 0, 1).endVertex();
        vertexBuilder.vertex(matrix, 0, 0, 1).color(255, 215, 0, 128).uv(1, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 0, 1).endVertex();
        vertexBuilder.vertex(matrix, 0, 1, 1).color(255, 215, 0, 128).uv(1, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 0, 1).endVertex();
        vertexBuilder.vertex(matrix, 1, 1, 1).color(255, 215, 0, 128).uv(0, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(0, 0, 1).endVertex();
        
        // West face
        vertexBuilder.vertex(matrix, 0, 0, 1).color(255, 215, 0, 128).uv(0, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(-1, 0, 0).endVertex();
        vertexBuilder.vertex(matrix, 0, 0, 0).color(255, 215, 0, 128).uv(1, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(-1, 0, 0).endVertex();
        vertexBuilder.vertex(matrix, 0, 1, 0).color(255, 215, 0, 128).uv(1, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(-1, 0, 0).endVertex();
        vertexBuilder.vertex(matrix, 0, 1, 1).color(255, 215, 0, 128).uv(0, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(-1, 0, 0).endVertex();
        
        // East face
        vertexBuilder.vertex(matrix, 1, 0, 0).color(255, 215, 0, 128).uv(0, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(1, 0, 0).endVertex();
        vertexBuilder.vertex(matrix, 1, 0, 1).color(255, 215, 0, 128).uv(1, 0).overlayCoords(combinedOverlay).uv2(combinedLight).normal(1, 0, 0).endVertex();
        vertexBuilder.vertex(matrix, 1, 1, 1).color(255, 215, 0, 128).uv(1, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(1, 0, 0).endVertex();
        vertexBuilder.vertex(matrix, 1, 1, 0).color(255, 215, 0, 128).uv(0, 1).overlayCoords(combinedOverlay).uv2(combinedLight).normal(1, 0, 0).endVertex();
        
        matrixStack.popPose();
    }
    
    private void renderGlowingOutline(MatrixStack matrixStack, IRenderTypeBuffer buffer, float partialTicks) {
        matrixStack.pushPose();
        
        // Animate the glow intensity
        float time = (System.currentTimeMillis() + partialTicks) / 1000.0f;
        float glowIntensity = (float) (0.5 + 0.3 * Math.sin(time * 2.0));
        
        // Scale slightly larger for the glow outline
        matrixStack.scale(1.05f, 1.05f, 1.05f);
        matrixStack.translate(-0.025f, -0.025f, -0.025f);
        
        // Rotate the glow effect slowly
        matrixStack.mulPose(Vector3f.YP.rotationDegrees(time * 20));
        
        // Use a translucent render type for the glow
        IVertexBuilder glowBuilder = buffer.getBuffer(RenderType.translucent());
        Matrix4f matrix = matrixStack.last().pose();
        
        // Render a subtle golden glow around the block
        int red = (int) (255 * glowIntensity);
        int green = (int) (215 * glowIntensity);
        int blue = (int) (0 * glowIntensity);
        int alpha = (int) (64 * glowIntensity);
        
        // Simple glow effect on top face
        glowBuilder.vertex(matrix, 0, 1.01f, 0).color(red, green, blue, alpha).uv(0, 0).overlayCoords(0).uv2(240).normal(0, 1, 0).endVertex();
        glowBuilder.vertex(matrix, 1, 1.01f, 0).color(red, green, blue, alpha).uv(1, 0).overlayCoords(0).uv2(240).normal(0, 1, 0).endVertex();
        glowBuilder.vertex(matrix, 1, 1.01f, 1).color(red, green, blue, alpha).uv(1, 1).overlayCoords(0).uv2(240).normal(0, 1, 0).endVertex();
        glowBuilder.vertex(matrix, 0, 1.01f, 1).color(red, green, blue, alpha).uv(0, 1).overlayCoords(0).uv2(240).normal(0, 1, 0).endVertex();
        
        matrixStack.popPose();
    }
}
